<template>
	<view class="themeDetailPage mheight-100vh flex flex-column">
		<navigationBar title="制作名片" border customBack @clickBack="onClickBack" />
		<view class="p-4 flex-1 flex flex-column">
			<view class="tab-list flex justify-between mb-base px-1">
				<view
					v-for="(item, index) in tabList"
					:key="index"
					class="tab-item position-relative"
					@tap="onToggleTab(index)"
				>
					<image
						:src="activeTabIndex == index ? item.activeImg : item.img"
						mode="aspectFit"
						class="width-100 height-100"
					></image>
				</view>
			</view>
			<template v-if="isStep1">
				<wd-form ref="formRef" :model="form" :rules="formRules">
					<view class="rounded-16 p-base shadow bg-white mb-4">
						<wd-cell-group border>
							<wd-input
								required
								label="姓名"
								:label-width="labelWidth"
								prop="name"
								clearable
								v-model="form.name"
								placeholder="请输入姓名"
							/>
							<wd-input
								label="企业/组织"
								:label-width="labelWidth"
								prop="company"
								clearable
								v-model="form.company"
								placeholder="请输入企业/组织"
							/>
							<wd-input
								label="职位/头衔"
								:label-width="labelWidth"
								prop="job"
								clearable
								v-model="form.job"
								placeholder="请输入职位/头衔"
							/>
							<wd-input
								label="联系电话"
								:label-width="labelWidth"
								prop="phoneNumber"
								clearable
								v-model="form.phoneNumber"
								placeholder="请输入联系电话"
							/>
							<wd-input
								label="微信号"
								:label-width="labelWidth"
								prop="weixin"
								clearable
								v-model="form.weixin"
								placeholder="请输入微信号"
							/>
							<wd-cell title="微信二维码" :title-width="labelWidth" prop="weixinCode">
								<view class="my-upload">
								<wd-upload :file-list="form.weixinCode"  @change="onFileChange" custom-preview-class='custom-preview-class'>
									
									<view class="rounded-8 border p-4 bg-light upload-iconwrap flex justify-center align-center">
										<view class="upload-iconbox">
											<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
										</view>
									</view>
								</wd-upload>
								</view>
							</wd-cell>
							<wd-input
								label="邮箱"
								:label-width="labelWidth"
								prop="email"
								clearable
								v-model="form.email"
								placeholder="请输入邮箱"
							/>
							<!-- <view class="position-relative flex align-center textarea-custom-class-wrap width-100">
								<view class="flex-1 ">
									<wd-textarea :no-border="true" v-model="form.address" :label-width="labelWidth" placeholder="请输入地址" auto-height label="地址" custom-class="textarea-custom-class">
										
									</wd-textarea>
								</view>
								<view class="flex align-center flex-shrink">
									<view v-if="form.address" class="px-1" @tap.stop='onClearAddress'>
										<wd-icon name="error-fill" size="16px"></wd-icon>
									</view>
									<view class="dingwei-box px-1" @tap.stop='onChooseAddress'>
										<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
									</view>
								</view>
							</view> -->
							<wd-input
								label="地址"
								:label-width="labelWidth"
								prop="address"
								v-model="form.address"
								placeholder="请输入地址"
							>
							<template #suffix>
								<view class="flex align-center">
									<view v-if="form.address" class="px-1" @tap.stop='onClearAddress'>
										<wd-icon name="error-fill" size="16px"></wd-icon>
									</view>
									<view class="dingwei-box px-1" @tap.stop='onChooseAddress'>
										<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
									</view>
								</view>
							</template>
							</wd-input>
							<view class="">
								<wd-input label="个人介绍" label-width="100%" disabled />
								<wd-textarea
									label=""
									label-width="0px"
									prop="introduce"
									clearable
									v-model="form.introduce"
									placeholder="请输入个人介绍"
								/>
							</view>
						</wd-cell-group>
					</view>
					<view class="rounded-16 p-base shadow bg-white mb-4">
						<view class="font-weight-bold font-16 text-first mb-4"> 社交账号 </view>
						<wd-cell-group>
							<view class="form-input">
								<wd-input prop="bilicount" clearable v-model="form.bilicount">
									<template #label>
										<view class="flex align-center text-first font-14">
											<view class="input-icon-box mr-0-8">
												<image
													src="/static/images/<EMAIL>"
													mode="aspectFit"
													class="width-100 height-100"
												></image>
											</view>
											哔哩哔哩
										</view>
									</template>
								</wd-input>
							</view>
							<view class="form-input">
								<wd-input prop="douyincount" clearable v-model="form.douyincount">
									<template #label>
										<view class="flex align-center text-first font-14">
											<view class="input-icon-box mr-0-8">
												<image
													src="/static/images/<EMAIL>"
													mode="aspectFit"
													class="width-100 height-100"
												></image>
											</view>
											抖音
										</view>
									</template>
								</wd-input>
							</view>
							<view class="form-input">
								<wd-input prop="xiaohongshucount" clearable v-model="form.xiaohongshucount">
									<template #label>
										<view class="flex align-center text-first font-14">
											<view class="input-icon-box mr-0-8">
												<image
													src="/static/images/<EMAIL>"
													mode="aspectFit"
													class="width-100 height-100"
												></image>
											</view>
											小红书
										</view>
									</template>
								</wd-input>
							</view>
							<view class="form-input">
								<wd-input prop="weibocount" clearable v-model="form.weibocount">
									<template #label>
										<view class="flex align-center text-first font-14">
											<view class="input-icon-box mr-0-8">
												<image
													src="/static/images/<EMAIL>"
													mode="aspectFit"
													class="width-100 height-100"
												></image>
											</view>
											weibo
										</view>
									</template>
								</wd-input>
							</view>
						</wd-cell-group>
					</view>
					<view class="">
						<wd-button type="primary" size="large" @click="onGoNext" block
							>下一步</wd-button
						>
					</view>
				</wd-form>
			</template>
			<template v-if="isStep2">
				<view
					class="flex-1 rounded-16 bg-white position-relative shadow mb-4 flex flex-column avavtar-body overflow-hidden"
				>
					<view class="avatar-tab-list flex position-relative flex-shrink">
						<view
							v-for="(item, index) in avatarTabList"
							:key="index"
							class="avatar-tab-item span-10 py-4 text-center flex flex-column justify-center align-center font-14"
							:class="
								activeAvatarTabIndex == index
									? `font-weight-bold active ${'active' + index}`
									: ''
							"
							@tap="onToggleAvatarTab(index)"
						>
							<view class="title">
								{{ item.title }}
							</view>
							<view v-if="activeAvatarTabIndex == index" class="line mt-1"></view>
						</view>
					</view>
					<view class="flex-1 width-100 flex justify-center align-center flex-column  box-border position-relative ">
						<template v-if="activeAvatarTabIndex == 0">
							<template v-if="false">
								<view class="card-swiper mt-4 flex-1 " id='swiper-wrap'>
								 <wd-swiper
									:indicator="false"
								    autoplay
									:height="swiperNodeHeight"
								    v-model:current="swiperCurrent"
								    custom-indicator-class="custom-indicator-class"
								    custom-image-class="custom-image"
								    custom-next-image-class="custom-image-prev"
								    custom-prev-image-class="custom-image-prev"
								    :list="swiperList"
								    previousMargin="50px"
								    nextMargin="50px"
								  ></wd-swiper>
								  </view>
								  <view class="flex-shrink mt-base  mb-4 sanjiao-box d-block mx-auto width-100">
								  	<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
								  </view>
								  <view class="flex-shrink text-fourth font-14 text-center pb-4">
								  	选择形象照相册中的照片
								  </view>
							</template>
							<template v-if="true">
							<view class="picXx-box mb">
								<image
									src="/static/images/<EMAIL>"
									mode="aspectFit"
									class="width-100 height-100"
								></image>
							</view>
							<view class="mb-4 text-center text-third font-14">还没ai形象照呢</view>
							<view
								class="text-center text-theme-brand2 font-14 mb-4"
								@tap="onGetAvatar"
							>
								去生成
							</view>
							</template>
						</template>
						<template v-if="activeAvatarTabIndex==1">
							<view class="avavtar-upload-wrap flex-1 flex justify-center align-center  flex-column rounded-12 overflow-hidden position-relative" @tap='onUploadAvatar'>
								<template v-if="avatarImgUrl">
									<view class="position-absolute top-0 right-0 left-0 bottom-0 flex  align-center justify-center">
										<image :src="avatarImgUrl" mode="aspectFill" class="width-100 height-100"></image>
									</view>
								</template>
								<template v-else>
									<view class="xxUpload-box mb-base">
										<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
									</view>
									<view class="text-theme-brand font-16 text-center">上传图片</view>
								</template>
							</view>
						</template>
						<!-- <view class="py-4"></view> -->
					</view>
				</view>
				<view class="flex-shrink">
					<wd-button type="primary" size="large" @click="onSubmit" block>保存</wd-button>
				</view>
			</template>
		</view>
	</view>
</template>

<script setup>
	import { ref, reactive, computed } from 'vue'
	import {onReady} from "@dcloudio/uni-app"
import navigationBar from '@/components/common/navigationBar.vue'
// 常量
const TAB_LIST = [
	{ img: '/static/images/<EMAIL>', activeImg: '/static/images/<EMAIL>' },
	{ img: '/static/images/<EMAIL>', activeImg: '/static/images/<EMAIL>' },
]
const AVAVTARTAB_LIST = [{ title: 'AI形象照' }, { title: '自主上传' }]
// 变量
const tabList = ref(TAB_LIST) //页面tab
const activeTabIndex = ref(0)
const avatarTabList = ref(AVAVTARTAB_LIST)
const activeAvatarTabIndex = ref(0)
const avatarImgUrl=ref("") // 上传的图片
const formRef=ref()
const labelWidth='80px'
const form = reactive({
	name: '',
	company:"",
	job:"",
	phoneNumber:"",
	weixin:"",
	weixinCode:"",
	email:"",
	address:"",
	introduce:"",
	bilicount:"",
	douyincount:"",
	xiaohongshucount:"",
	weibocount:"",
	latitude:"",
	longitude:""
}) // 表单数据
const formRules={
	name: [
	    {
	      required: true,
	      message: "请输入姓名",
	      validator: (value) => {
			  console.log("value",value)
	        if (value) {
	          return Promise.resolve()
	        } else {
	          return Promise.reject(t('qing-shu-ru-you-hui-quan-ming-cheng'))
	        }
	      }
	    }
	  ],
}  // 验证规则
const swiperNodeHeight=ref(200)
const swiperCurrent=ref(0)
const swiperList=ref([
	'https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/lisa/<EMAIL>',
	'https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/lisa/<EMAIL>',
	'https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/lisa/<EMAIL>'
])// 轮播数据

const isStep1 = computed(() => {
	return activeTabIndex.value == 0
})
const isStep2 = computed(() => {
	return activeTabIndex.value == 1
})

//钩子
onReady(()=>{
	initPage()
})

//函数
//初始化页面
function initPage(){
	const query=uni.createSelectorQuery()
	query.select("#swiper-wrap")
	.boundingClientRect((data)=>{
		console.log(data)
		swiperNodeHeight.value=data.height
		console.log("swiperNodeHeight.value",swiperNodeHeight.value)
	})
	.exec()
}
// 上传图片
function onUploadAvatar() {
	uni.chooseImage({
		count: 1,
		sizeType: ['original', 'compressed'], 
		sourceType: ['album'], //从相册选择
		success: function (res) {
			avatarImgUrl.value=res.tempFilePaths
			console.log(JSON.stringify(res.tempFilePaths));
		}
	})
}
function onClearAddress() {
	form.address=""
	form.latitude=""
	form.longitude=""
}
// 选择地址
function onChooseAddress() {
	uni.chooseLocation({
		// longitude:form.longitude,
		// latitude:form.latitude,
		success: function (res) {
			console.log(res)
			form.address=res.address
			form.latitude=res.latitude
			form.longitude=res.longitude
			console.log(form.address)
		},
		fail:err=>{
			console.log("chooseLocation:err",err)
		}
	});
}
// 上传微信二维码
function onFileChange() {
	
}
// 去生成
function onGetAvatar() {
	activeAvatarTabIndex.value = 1
}
//切换tab
function onToggleTab(index) {
	activeTabIndex.value = index
}
function onToggleAvatarTab(index) {
	activeAvatarTabIndex.value = index
}
//返回
function onClickBack() {
	if (isStep1.value) {
		// uni.navigateBack()
		uni.switchTab({
			url:"/pages/cardList/cardList"
		})
	}
	if (isStep2.value) {
		activeTabIndex.value = 0
	}
}
// 下一步
function onGoNext() {
	console.log("form",form)
	 formRef.value.validate().then(({valid,errors}) => {
	      if (valid) {
	       activeTabIndex.value = 1 
	      }else{
			  uni.showToast({
			  	icon:"none",
				title:"请先填写完表单!"
			  })
		  }
	    })
	    .catch((error) => {
	      console.log(error, 'error')
	    })
}
// 提交
function onSubmit() {}
</script>

<style lang="scss" scoped>
	.dingwei-box{
		width:48rpx;
		height: 48rpx;
	}
	.upload-iconbox{
		width:48rpx;
		height: 48rpx;
	}
.avavtar-body {
	
}
.avatar-tab-list{
	background: linear-gradient(#eaf2f3 2%,transparent);
}
.avatar-tab-item {
	$afterBoxW: 30rpx;
	position: relative;
	padding:40rpx 10rpx;
	&.active {
		background-color: #fff;
		z-index: 5;
		// &:after {
		// 	content: '';
		// 	position: absolute;
		// 	left: 0;
		// 	right: 0;
		// 	bottom: -15rpx;
		// 	height: 30rpx;
		// 	background-color: #fff;
		// 	// border:1px solid red;
		// }
	}
	&.active0 {
		border-top-right-radius: 50rpx;
	}
	&.active1 {
		border-top-left-radius: 50rpx;
	}
	.line {
		width: 40rpx;
		height: 8rpx;
		border-radius: 4rpx;
		background: linear-gradient(90deg, #4a8da9 20%, #98cdd2 100%);
	}
}
.form-input {
	position: relative;
	&:after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 18rpx;
		right: 18rpx;
		height: 2rpx;
		background-color: rgba(#e8e8e8, 0.5);
	}
}
.textarea-custom-class-wrap{
	position:relative;
	&:after{
		position: absolute;
		    display: block;
		    content: "";
			top:0;
		    left: 20rpx;
		    right:0rpx;
		    height: 2rpx;
		    background-color: rgba(#e8e8e8, 0.5);
	}
	:deep(.textarea-custom-class){
		&:after{
			content:"";
			display:none!important;
		}
	}
}

.input-icon-box {
	width: 40rpx;
	height: 40rpx;
}
.tab-list {
	.tab-item {
		width: 308rpx;
		height: 172rpx;
	}
}
.picXx-box {
	width: 224rpx;
	height: 224rpx;
}
.avavtar-upload-wrap{
	width:384rpx;
	height: 100%;
	max-height: 524rpx;
	border:2rpx solid #202143;
	.xxUpload-box{
		width:88rpx;
		height: 88rpx;
	}
}
.my-upload{
	:deep(.wd-upload__mask){
		border-radius:12rpx!important;
	}
	.upload-iconwrap{
		width:80rpx;
		height: 80rpx;
	}
}
.card-swiper {
  --wot-swiper-radius: 0;
  --wot-swiper-item-padding: 0 24rpx;
  --wot-swiper-nav-dot-color: #e7e7e7;
  --wot-swiper-nav-dot-active-color: #4d80f0;
  box-sizing: border-box;
  :deep(.custom-indicator-class) {
    bottom: -16px;
  }
  :deep(.custom-image) {
    border-radius: 12rpx;
	// height:400rpx !important;
  }
  :deep(.custom-image-prev) {
    // height:30vh !important;
	position: relative;
	transform: scaleY(0.7);
	&:after{
		content:"";
		position:absolute;
		z-index: 5;
		left:0;
		top:0;
		right:0;
		bottom:0;
		background-color: rgba(#fff,0.5);
	}
  }
}
.sanjiao-box{
	width:32rpx;
	height: 32rpx;
}
</style>
