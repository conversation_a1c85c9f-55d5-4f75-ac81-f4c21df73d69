<template>
	<view class="wrap height-100vh overflow-hidden flex flex-column position-relative box-border">
		<navigation-bar :border='false'>
			<template #right>
				<view class="logo-box mt-5">
					<image src="/static/images/guidimg/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
				</view>
			</template>
		</navigation-bar>
		<view class="flex-1 position-relative p-4">
			<view class="position-absolute top-0 zindex-1 right-0 left-0 bottom-0 guid-wrap px-4 pt-4">
				<view class="guid-box width-100 height-100 overflow-hidden">
					<image src="/static/images/guidimg/guid-picture.png" mode="widthFix" class="width-100 "></image>
				</view>
			</view>
			<view class="position-absolute left-0 zindex-2 right-0 bottom-0  text-body flex flex-column justify-center align-center">
				<view class="title-box">
					<image src="/static/images/guidimg/<EMAIL>" mode="widthFix" class="width-100"></image>
				</view>
				<view class="dot-wrap flex align-center justify-center my-5">
					<view v-for="(item,index) in 3" :key="index" class="dot-box mr-2" :class="index==0?'active':''"></view>
				</view>
				<view class="flex-shrink flex justify-center align-center footer-btn mx-auto bg-white text-first font-16 font-weight-bold" @tap='onOpenPage'>
					制作我的名片
					<view class="right-icon-box ml-4">
						<image src="/static/images/guidimg/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {onLoad} from "@dcloudio/uni-app"
	import { useUserStore } from '@/store/user';
	
	
	const userStore=useUserStore()
	//钩子
	onLoad(()=>{
		console.log("userStore",userStore.skimCount)
		if(userStore.skimCount==0){
			userStore.saveSkimCount(1)
		}else{
			uni.switchTab({
				url:"/pages/cardList/cardList"
			})
		}
	})
	//打开系统页面
	function onOpenPage() {
		uni.navigateTo({
			url:"/pages/cardEditStep1/cardEditStep1"
		})
	}
</script>

<style lang="scss" scoped>
	.wrap{
		background:radial-gradient( circle at 10% 2%, #FBFBF5, #E4ECEE);
		// padding-bottom: 120rpx;
	}
	.guid-wrap{
		
	}
	.guid-box{
		border-top-right-radius: 60rpx;
		border-top-left-radius: 60rpx;
	}
	.logo-box{
		height:50rpx;
		width: 232rpx;
	}
	.footer-btn{
	  width: 400rpx;
	  height: 88rpx;
	  border-radius: 88rpx;
	}
	.right-icon-box{
		width:40rpx;
		height:40rpx;
	}
	.text-body{
		// border:1px solid red;
		padding-top:250rpx;
		padding-bottom: 120rpx;
		background:linear-gradient(180deg,transparent 10%,#E4ECEE 30%);
		opacity: 0.9;
		.title-box{
			width: 58.66%;
			max-width: 440rpx;
			opacity: 1;
		}
		.dot-wrap{
			margin-bottom: 60rpx;
			opacity: 1;
		}
		.dot-box{
			width:16rpx;
			height: 16rpx;
			border-radius: 100%;
			background-color: transparent;
			border:2rpx solid #202143;
			&.active{
				background-color: #202143;
			}
		}
	}
</style>
