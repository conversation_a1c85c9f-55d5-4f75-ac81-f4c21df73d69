<template>
	<view class="tab-list flex justify-between mb-base px-1">
		<view class="tab-item position-relative">
			<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
		</view>
		<view class="tab-item position-relative">
			<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
		</view>
	</view>
</template>

<script setup>
	import {defineProps} from "vue"
	const props=defineProps({
		activeIndex:{
			type:Number,
			default:0
		}
	})
</script>

<style>
</style>