<template>
	<view class="wrap mheight-100vh themePage box-border flex flex-column">
		<navigationBar title="我的名片" :showBack="false" classname="mb-1 flex-shrink" />
		<view class="p-4 mt-2 flex-1 flex">
			<view class="picture-wrap flex-1 flex flex-column">
				<view class="position-relative flex-1 picture-box rounded-12  overflow-hidden">
					<view class=" position-absolute top-0 right-0 left-0 bottom-0 zindex-1">
						<image :src="picUrl" mode="aspectFill" class="width-100 height-100"></image>
					</view>
					<view class="position-absolute left-0 top-0 right-0 bottom-0 zindex-2 p-base flex flex-column text-white">
						<view class="flex-shrink flex align-center">
							<view class="yuan-box mr-0-8">
								<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
							</view>
							<view class="mr-base font-14">DEMO演示</view>
							<view class="icon-right-box mr-0-8">
								<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
							</view>
						</view>
						<view class="flex-1"></view>
						<view class="flex-shrink">
							<view class="replay-box mb-2" hover-class="bg-hover" >
							  <image
							    src="/static/images/<EMAIL>"
							    mode="aspectFit"
							    class="width-100 height-100"
							  ></image>
							</view>
							<view class="mb-auto flex-shrink flex align-center position-relative">
							  <view class="more-box mr-4">
							    <image
							      src="/static/images/<EMAIL>"
							      mode="aspectFit"
							      class="width-100 height-100"
							    ></image>
							  </view>
							  <view class="flex-1">
							    <view class="font-weight-bold font-20 mb">Lisa</view>
							    <view class="flex align-center font-14">
							      EaseGYM健身中心
							      <span class="mx-1">|</span>
							      金牌教练
							    </view>
							  </view>
							  <view class="more1-box ml-auto mb-0-8">
							    <image
							      src="@/static/images/<EMAIL>"
							      mode="aspectFit"
							      class="width-100 height-100"
							    ></image>
							  </view>
							</view>
						</view>
					</view>
				</view>
				<view class="flex-shrink px-4">
					<view class="border1"></view>
					<view class="px-4">
						<view class="border2"></view>
					</view>
				</view>
			</view>
		</view>
		<view class="flex-shrink flex justify-center mb-4">
			<view hover-class="bg-hover" class="build-btn border py-2 text-center bg-white border-theme-brand text-theme-brand font-14 bg-white rounded-20" @tap="onEdit">制作我的名片</view>
		</view>
	</view>
</template>

<script setup>
import navigationBar from '@/components/common/navigationBar.vue';
import { ref } from 'vue';
const picUrl = ref('https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/lisa/<EMAIL>');
function onEdit() {
	uni.navigateTo({
		url:"/pages/cardEditStep1/cardEditStep1"
	})
}
</script>

<style lang="scss" scoped>
	.more-box {
	  width: 12rpx;
	  height: 66rpx;
	}
	.yuan-box{
		width: 64rpx;
		height: 64rpx;
	}
	.icon-right-box{
		width: 32rpx;
		height: 32rpx;
	}
	.replay-box {
	  width: 72rpx;
	  height: 72rpx;
	  margin-left: -10rpx;
	}
	.more1-box{
		width:64rpx;
		height: 64rpx;
	}
.picture-wrap {
  $left: 40rpx;
  $radius: 24rpx;
  position: relative;
  .picture-box {
    position: relative;
    z-index: 5;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5);
  }
  .border1 {
    width: 100%;
    border-bottom-left-radius: $radius;
    border-bottom-right-radius: $radius;
    height: 48rpx;
    background-color: #d6dee2;
    box-shadow: -2rpx 2rpx 4rpx 0rpx rgba(#ffffff, 0.35);
  }
  .border2 {
    width: 100%;
    border-bottom-left-radius: $radius;
    border-bottom-right-radius: $radius;
    height: 44rpx;
    background-color: #f2f5f6;
    box-shadow: -2rpx 2rpx 4rpx 0rpx rgba(#ffffff, 0.35);
  }
}
.build-btn {
  width: 400rpx;
}
</style>
