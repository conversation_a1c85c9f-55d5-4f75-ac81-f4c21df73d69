<template>
  <view class="flex align-center width-100" :class="[classname_,fullWidth ? 'position-relative left-0 right-0' : '']">
    <view class="flex-1" :style="borderStyle"></view>
    <slot v-if='title'><text class="mx-2 flex-shrink font text-third">{{title}}</text></slot>
    <view class="flex-1" :style="borderStyle"></view>
  </view>
</template>

<script>
  export default {
    props:{
      title:{
        type:String,
        default:""
      },
      type:{
        type:String,
        default:""
      },
      classname:{
        type:String,
        default:""
      },
      fullWidth:{
        type:Boolean,
        default:false
      },
      height:{
        type:Number,
        default:2
      },
      color:{
        type:String,
        default:"#F1F1F1"
      },
	  opacity:{
		  type:String,
		  default:1
	  }
    },
    computed:{
      borderStyle () {
        let str=`height:${this.height}rpx;background-color:${this.color};opacity:${this.opacity}`
        return str
      },
      classname_ () {
        let str=this.type ? `border-bottom border-${this.type}`  : ''
        return [str,this.classname]
      }
    }
  }
</script>

<style scoped>
  .full-w{
    width:750rpx!important;
    transform: translateX(-30rpx);
  }

</style>
