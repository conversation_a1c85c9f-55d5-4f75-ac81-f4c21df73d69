/**
 * 认证相关工具函数
 */

/**
 * 检查是否已登录
 * @returns {Boolean}
 */
export function isLoggedIn() {
	return  true  // ceshi 
  // const loginRes = uni.getStorageSync('loginRes')
  // return !!(loginRes && loginRes.access_token)
}

/**
 * 跳转到登录页
 */
export function redirectToLogin() {
  uni.reLaunch({
    url: '/pages/login/login',
  })
}

/**
 * 清除登录信息
 */
export function clearLoginInfo() {
  uni.removeStorageSync('loginRes')
  uni.removeStorageSync('loginForm')
}

/**
 * 获取token
 * @returns {String}
 */
export function getToken() {
  const loginRes = uni.getStorageSync('loginRes')
  return loginRes ? loginRes.access_token : ''
}

/**
 * 设置token
 * @param {String} token
 */
export function setToken(token) {
  const loginRes = uni.getStorageSync('loginRes') || {}
  loginRes.access_token = token
  uni.setStorageSync('loginRes', loginRes)
}
