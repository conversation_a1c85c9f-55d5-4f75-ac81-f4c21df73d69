<template>
	<view class="navigationBar flex-shrink" :class="[classname,border?'border':'']">
		<view class="status_bar"></view>
		<view class="position-relative py-1 main">
			<slot name="right">
			<view v-if="showBack" class="back-box" @tap='onGoback'>
				<image src="/static/images/back.png" mode="aspectFit" class="width-100 height-100"></image>
			</view>
			</slot>
			<view class="flex justify-center text-first title">{{title}}</view>
		</view>
	</view>
</template>

<script setup>
	import { defineProps,defineEmits } from 'vue';
	const props=defineProps({
		title:{
			type:String,
			default:""
		},
		showBack:{
			type:Boolean,
			default:true
		},
		border:{
			type:Boolean,
			default:true
		},
		classname:{
			type:String,
			default:""
		},
		customBack:{
			type:Boolean,
			default:false
		}
	})
	const emits=defineEmits(['clickBack'])
	function onGoback() {
		console.log("55")
		if(props.customBack) {
			emits("clickBack")
		}else{
			uni.navigateBack()
		}
	}
		
</script>

<style lang="scss" scoped>
	
	.navigationBar{
		position: relative;
		z-index: 10;
		.border{
			border:none;
			border-bottom: 2rpx solid rgba(#D2D4DF,0.3);
		}
		.title{
			margin-top:16rpx;
		}
		.main{
			padding:16rpx 80rpx;
			position: relative;
		}
		.status_bar{
			height: var(--status-bar-height);
		    width: 100%;
		}
		.back-box{
			position: absolute;
			width:40rpx;
			height: 40rpx;
			left:40rpx;
		}
	}
	
</style>
